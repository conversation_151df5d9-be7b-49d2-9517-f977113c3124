/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type ApprovedPremisesUserPermission = 'cas1_adhoc_booking_create' | 'cas1_application_withdraw_others' | 'cas1_assess_appealed_application' | 'cas1_assess_application' | 'cas1_assess_placement_application' | 'cas1_assess_placement_request' | 'cas1_booking_create' | 'cas1_booking_change_dates' | 'cas1_booking_withdraw' | 'cas1_change_request_list' | 'cas1_change_request_view' | 'cas1_offline_application_view' | 'cas1_out_of_service_bed_create' | 'cas1_out_of_service_bed_create_bed_on_hold' | 'cas1_out_of_service_bed_cancel' | 'cas1_placement_appeal_create' | 'cas1_placement_appeal_assess' | 'cas1_placement_request_record_unable_to_match' | 'cas1_process_an_appeal' | 'cas1_transfer_create' | 'cas1_transfer_assess' | 'cas1_user_list' | 'cas1_user_management' | 'cas1_view_assigned_assessments' | 'cas1_view_cru_dashboard' | 'cas1_view_manage_tasks' | 'cas1_view_out_of_service_beds' | 'cas1_request_for_placement_withdraw_others' | 'cas1_space_booking_create' | 'cas1_space_booking_list' | 'cas1_space_booking_record_arrival' | 'cas1_space_booking_record_departure' | 'cas1_space_booking_record_non_arrival' | 'cas1_space_booking_record_keyworker' | 'cas1_space_booking_view' | 'cas1_space_booking_withdraw' | 'cas1_space_booking_shorten' | 'cas1_premises_capacity_report_view' | 'cas1_tasks_list' | 'cas1_premises_view' | 'cas1_premises_manage' | 'cas1_reports_view' | 'cas1_reports_view_with_pii';
