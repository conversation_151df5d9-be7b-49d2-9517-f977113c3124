/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type MigrationJobType = 'update_all_users_from_community_api' | 'update_sentence_type_and_situation' | 'update_booking_status' | 'update_task_due_dates' | 'update_users_pdu_by_api' | 'update_cas2_applications_with_assessments' | 'update_cas2_status_updates_with_assessments' | 'update_cas2_notes_with_assessments' | 'update_cas1_backfill_user_ap_area' | 'update_cas3_application_offender_name' | 'update_cas3_booking_offender_name' | 'update_cas3_bedspace_start_date' | 'update_cas3_domain_event_type_for_person_departed_updated' | 'update_cas1_applications_licence_expiry_date' | 'update_cas1_backfill_offline_application_name' | 'update_cas1_arson_suitable_to_arson_offences' | 'update_cas1_backfill_arson_suitable' | 'update_cas1_approved_premises_assessment_report_properties' | 'update_cas1_room_codes' | 'update_cas1_applications_with_offender' | 'update_cas3_bedspace_model_data' | 'update_cas3_void_bedspace_cancellation_data';
