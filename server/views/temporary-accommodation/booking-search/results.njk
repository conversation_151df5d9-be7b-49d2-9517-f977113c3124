{% from "govuk/components/table/macro.njk" import govukTable %}
{% from "../../components/search-by-crn-or-name-form/macro.njk" import searchByCrnOrNameForm%}
{% from "../../components/search-by-crn-or-name-results/macro.njk" import searchByCrnOrNameResults%}
{%- from "moj/components/sub-navigation/macro.njk" import mojSubNavigation -%}
{%- from "moj/components/pagination/macro.njk" import mojPagination -%}
{% from "govuk/components/back-link/macro.njk" import govukBackLink %}

{% extends "../../partials/layout.njk" %}
{% set pageTitle = "View bookings - " + applicationName %}
{% set mainClasses = "app-container govuk-body" %}

{# TODO Move this HTML to the searchByCrnOrNameResult once HTML can be standardised between referrals and bookings #}
{% set resultsHtml %}
    {% if (response.data|length > 0) or (not crnOrName) %}
        {{ govukTable({
            captionClasses: "govuk-table__caption--m",
            head: tableHeadings,
            rows: response.data
        }) }}

        {{ mojPagination(pagination) }}
    {% endif %}
{% endset %}

{% block beforeContent %}
    {{ govukBackLink({
        text: "Back",
        href: "/",
        classes: 'govuk-!-display-none-print'
    }) }}
{% endblock %}

{% block content %}
    {% set context = fetchContext() %}

    <h1 class="govuk-heading-l">{{ uiStatus | capitalize }} bookings</h1>

    {{ mojSubNavigation({
        label: 'Secondary navigation',
        items: subNavArr
    }) }}

    <div class="govuk-template govuk-!-padding-8 govuk-!-padding-bottom-static-3 govuk-!-margin-bottom-8">
        {{ searchByCrnOrNameForm({ 
            type: 'bookings',
            basePath: paths.bookings.search[uiStatus].index(), 
            uiStatus: uiStatus, 
            crnOrName: crnOrName
        })}}
    </div>

    {{ searchByCrnOrNameResults({
        type: 'bookings',
        errors: context.errors,
        resultsHtml: resultsHtml,
        uiStatus: uiStatus,
        crnOrName: crnOrName
    })}}

{% endblock %}
