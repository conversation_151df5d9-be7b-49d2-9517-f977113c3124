/* istanbul ignore file */

import { Services } from '../../../services'
import ArrivalsController from './arrivalsController'
import AssessmentsController from './assessmentsController'
import BedspaceSearchController from './bedspaceSearchController'
import BedspacesController from './bedspacesController'
import BookingSearchController from './bookingSearchController'
import BookingsController from './bookingsController'
import CancellationsController from './cancellationsController'
import ConfirmationsController from './confirmationsController'
import DashboardController from './dashboardController'
import DeparturesController from './departuresController'
import ExtensionsController from './extensionsController'
import LostBedsController from './lostBedsController'
import PremisesController from './premisesController'
import ReportsController from './reportsController'
import TurnaroundsController from './turnaroundsController'
import PremisesControllerV2 from './v2/premisesController'

export const controllers = (services: Services) => {
  const dashboardController = new DashboardController()
  const premisesController = new PremisesController(
    services.premisesService,
    services.bedspaceService,
    services.assessmentsService,
  )
  const premisesControllerV2 = new PremisesControllerV2(services.v2.premisesService)
  const bedspacesController = new BedspacesController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.assessmentsService,
  )
  const bookingsController = new BookingsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.personService,
    services.assessmentsService,
  )

  const confirmationsController = new ConfirmationsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.confirmationService,
  )
  const arrivalsController = new ArrivalsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.arrivalService,
  )
  const departuresController = new DeparturesController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.departureService,
  )
  const extensionsController = new ExtensionsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.extensionService,
  )
  const cancellationsController = new CancellationsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.cancellationService,
  )
  const turnaroundsController = new TurnaroundsController(
    services.premisesService,
    services.bedspaceService,
    services.bookingService,
    services.turnaroundService,
  )

  const reportsController = new ReportsController(services.reportService)

  const lostBedsController = new LostBedsController(
    services.lostBedService,
    services.premisesService,
    services.bedspaceService,
    services.assessmentsService,
  )

  const bedspaceSearchController = new BedspaceSearchController(
    services.bedspaceSearchService,
    services.assessmentsService,
  )
  const bookingSearchController = new BookingSearchController(services.bookingSearchService)

  const assessmentsController = new AssessmentsController(services.assessmentsService, services.timelineService)

  return {
    dashboardController,
    premisesController,
    bedspacesController,
    bookingsController,
    confirmationsController,
    arrivalsController,
    departuresController,
    extensionsController,
    cancellationsController,
    turnaroundsController,
    reportsController,
    lostBedsController,
    bedspaceSearchController,
    bookingSearchController,
    assessmentsController,
    v2: {
      premisesController: premisesControllerV2,
    },
  }
}

export {
  ArrivalsController,
  AssessmentsController,
  BedspaceSearchController,
  BedspacesController,
  BookingSearchController,
  BookingsController,
  CancellationsController,
  ConfirmationsController,
  DashboardController,
  ExtensionsController,
  LostBedsController,
  PremisesController,
  ReportsController,
  TurnaroundsController,
}
