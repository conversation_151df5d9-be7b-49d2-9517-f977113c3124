import ConsentGivenPage from './accommodation-need/consent/consentGiven'
import BackupContactPage from './accommodation-need/contact-details/backupContact'
import PopPhoneNumberPage from './accommodation-need/contact-details/popPhoneNumber'
import ProbationPractitionerPage from './accommodation-need/contact-details/probationPractitioner'
import AccommodationRequiredFromDatePage from './accommodation-need/eligibility/accommodationRequiredFromDate'
import EligibilityReasonPage from './accommodation-need/eligibility/eligibilityReason'
import ReleaseDatePage from './accommodation-need/eligibility/releaseDate'
import OffendingSummaryPage from './accommodation-need/sentence-information/offendingSummary'
import ReleaseTypePage from './accommodation-need/sentence-information/releaseType'
import SentenceExpiryPage from './accommodation-need/sentence-information/sentenceExpiry'
import SentenceLengthPage from './accommodation-need/sentence-information/sentenceLength'
import SentenceTypePage from './accommodation-need/sentence-information/sentenceType'
import ApprovalsForSpecificRisksPage from './assess-placement-risks-and-needs/approvals-for-specific-risks/approvalsForSpecificRisks'
import PreviousStaysPage from './assess-placement-risks-and-needs/behaviour-in-cas/previousStays'
import PreviousStaysDetailsPage from './assess-placement-risks-and-needs/behaviour-in-cas/previousStaysDetails'
import AdditionalLicenceConditionsPage from './assess-placement-risks-and-needs/licence-conditions/additionalLicenceConditions'
import AccommodationSharingPage from './assess-placement-risks-and-needs/placement-considerations/accommodationSharing'
import AntiSocialBehaviourPage from './assess-placement-risks-and-needs/placement-considerations/antiSocialBehaviour'
import CooperationPage from './assess-placement-risks-and-needs/placement-considerations/cooperation'
import RiskManagementPlanPage from './assess-placement-risks-and-needs/placement-considerations/riskManagementPlan'
import RoshLevelPage from './assess-placement-risks-and-needs/placement-considerations/roshLevel'
import SubstanceMisusePage from './assess-placement-risks-and-needs/placement-considerations/substanceMisuse'
import AcctAlertsPage from './assess-placement-risks-and-needs/prison-information/acctAlerts'
import AdjudicationsPage from './assess-placement-risks-and-needs/prison-information/adjudications'
import CheckYourAnswersPage from './check-your-answers/check-your-answers/checkYourAnswersPage'
import ConfirmDetailsPage from './confirmDetails'
import EnterCRNPage from './enterCrn'
import ListPage from './list'
import CrsSubmittedPage from './required-referrals/accommodation-referral-details/crsSubmitted'
import DtrDetailsPage from './required-referrals/accommodation-referral-details/dtrDetails'
import DtrSubmittedPage from './required-referrals/accommodation-referral-details/dtrSubmitted'
import OtherAccommodationOptionsPage from './required-referrals/accommodation-referral-details/otherAccommodationOptions'
import NeedsPage from './requirements-for-placement/disability-cultural-and-specific-needs/needs'
import PropertyAttributesOrAdaptationsPage from './requirements-for-placement/disability-cultural-and-specific-needs/propertyAttributesOrAdaptations'
import ReligiousOrCulturalNeedsPage from './requirements-for-placement/disability-cultural-and-specific-needs/religiousOrCulturalNeeds'
import FoodAllergiesPage from './requirements-for-placement/food-allergies/foodAllergies'
import MoveOnPlanPage from './requirements-for-placement/move-on-plan/moveOnPlan'
import AlternativePduPage from './requirements-for-placement/placement-location/alternativePdu'
import AlternativePduReasonPage from './requirements-for-placement/placement-location/alternativePduReason'
import CaringResponsibilitiesPage from './requirements-for-placement/safeguarding-and-support/caringResponsibilities'
import LocalConnectionsPage from './requirements-for-placement/safeguarding-and-support/localConnections'
import SafeguardingAndVulnerabilityPage from './requirements-for-placement/safeguarding-and-support/safeguardingAndVulnerability'
import SupportInTheCommunityPage from './requirements-for-placement/safeguarding-and-support/supportInTheCommunity'
import SelectOffencePage from './selectOffence'
import StartPage from './startPage'
import SubmissionConfirmation from './submissionConfirmation'
import TaskListPage from './taskListPage'
import ApplicationFullPage from './full'
import HistoryOfSexualOffencePage from './accommodation-need/offence-and-behaviour-summary/historyOfSexualOffence'
import RegisteredSexOffenderPage from './accommodation-need/offence-and-behaviour-summary/registeredSexOffender'
import ConcerningSexualBehaviourPage from './accommodation-need/offence-and-behaviour-summary/concerningSexualBehaviour'
import HistoryOfArsonOffencePage from './accommodation-need/offence-and-behaviour-summary/historyOfArsonOffence'
import ConcerningArsonBehaviourPage from './accommodation-need/offence-and-behaviour-summary/concerningArsonBehaviour'

export {
  AccommodationRequiredFromDatePage,
  AccommodationSharingPage,
  AcctAlertsPage,
  AdditionalLicenceConditionsPage,
  AdjudicationsPage,
  AlternativePduPage,
  AlternativePduReasonPage,
  AntiSocialBehaviourPage,
  ApprovalsForSpecificRisksPage,
  BackupContactPage,
  CaringResponsibilitiesPage,
  CheckYourAnswersPage,
  ConcerningArsonBehaviourPage,
  ConfirmDetailsPage,
  ConsentGivenPage,
  CooperationPage,
  CrsSubmittedPage,
  DtrDetailsPage,
  DtrSubmittedPage,
  EligibilityReasonPage,
  EnterCRNPage,
  FoodAllergiesPage,
  HistoryOfArsonOffencePage,
  ListPage,
  LocalConnectionsPage,
  MoveOnPlanPage,
  NeedsPage,
  OffendingSummaryPage,
  OtherAccommodationOptionsPage,
  PopPhoneNumberPage,
  PreviousStaysDetailsPage,
  PreviousStaysPage,
  ProbationPractitionerPage,
  PropertyAttributesOrAdaptationsPage,
  RegisteredSexOffenderPage,
  ReleaseDatePage,
  ReleaseTypePage,
  ReligiousOrCulturalNeedsPage,
  RiskManagementPlanPage,
  RoshLevelPage,
  SafeguardingAndVulnerabilityPage,
  SelectOffencePage,
  SentenceExpiryPage,
  SentenceLengthPage,
  SentenceTypePage,
  ConcerningSexualBehaviourPage,
  HistoryOfSexualOffencePage,
  StartPage,
  SubmissionConfirmation,
  SubstanceMisusePage,
  SupportInTheCommunityPage,
  TaskListPage,
  ApplicationFullPage,
}
