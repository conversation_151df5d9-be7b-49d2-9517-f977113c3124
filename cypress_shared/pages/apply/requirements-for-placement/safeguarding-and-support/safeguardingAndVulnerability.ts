import type { TemporaryAccommodationApplication } from '@approved-premises/api'
import paths from '../../../../../server/paths/apply'
import ApplyPage from '../../applyPage'

export default class SafeguardingAndVulnerabilityPage extends ApplyPage {
  constructor(application: TemporaryAccommodationApplication) {
    super(
      'Safeguarding and support',
      application,
      'safeguarding-and-support',
      'safeguarding-and-vulnerability',
      paths.applications.show({
        id: application.id,
      }),
    )
  }

  completeForm() {
    this.completeYesNoInputWithDetailFromPageBody('concerns')
  }
}
