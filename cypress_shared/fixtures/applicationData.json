{"offence-and-behaviour-summary": {"history-of-sexual-offence": {"historyOfSexualOffence": "no"}, "concerning-sexual-behaviour": {"concerningSexualBehaviour": "yes", "concerningSexualBehaviourDetail": "Sexual behaviour concerns details"}, "history-of-arson-offence": {"historyOfArsonOffence": "no"}, "concerning-arson-behaviour": {"concerningArsonBehaviour": "yes", "concerningArsonBehaviourDetail": "Arson behaviour concerns details"}}, "sentence-information": {"offending-summary": {"summary": "A brief summary of offending history"}, "sentence-type": {"sentenceType": "extendedDeterminate"}, "sentence-length": {"years": "2", "months": "", "weeks": "", "days": "4"}, "sentence-expiry": {"sentenceExpiryDate": "2124-01-19", "sentenceExpiryDate-year": "2124", "sentenceExpiryDate-month": "1", "sentenceExpiryDate-day": "19"}, "release-type": {"releaseTypes": ["fixedTermRecall", "parole"], "fixedTermRecallStartDate": "2024-01-19", "fixedTermRecallStartDate-year": "2024", "fixedTermRecallStartDate-month": "1", "fixedTermRecallStartDate-day": "19", "fixedTermRecallEndDate": "2024-07-09", "fixedTermRecallEndDate-year": "2024", "fixedTermRecallEndDate-month": "7", "fixedTermRecallEndDate-day": "9", "paroleStartDate": "2122-04-01", "paroleStartDate-year": "2122", "paroleStartDate-month": "4", "paroleStartDate-day": "1", "paroleEndDate": "2122-07-18", "paroleEndDate-year": "2122", "paroleEndDate-month": "7", "paroleEndDate-day": "18"}}, "contact-details": {"practitioner-name": {"name": "Practitioner Name"}, "practitioner-email": {"email": "<EMAIL>"}, "practitioner-phone": {"phone": "01234 56780"}, "practitioner-pdu": {"id": "de1bf2c5-fd1b-4a73-b725-f4fc38a67dc8", "name": "Devon and Torbay"}, "probation-practitioner": {"name": "Practitioner Name", "phone": "01234 56780", "email": "<EMAIL>", "pdu": {"id": "de1bf2c5-fd1b-4a73-b725-f4fc38a67dc8", "name": "Devon and Torbay"}}, "backup-contact": {"name": "Backup Name", "phone": "09876 54321", "email": "<EMAIL>"}, "pop-phone-number": {"phone": "01235 467890"}}, "eligibility": {"eligibility-reason": {"reason": "homelessFromApprovedPremises"}, "release-date": {"releaseDate": "2123-09-02", "releaseDate-year": "2123", "releaseDate-month": "9", "releaseDate-day": "2"}, "accommodation-required-from-date": {"accommodationRequiredFromDate": "2123-09-16", "accommodationRequiredFromDate-year": "2123", "accommodationRequiredFromDate-month": "9", "accommodationRequiredFromDate-day": "16"}}, "consent": {"consent-given": {"consentGiven": "yes"}}, "licence-conditions": {"additional-licence-conditions": {"conditions": ["civilOrders", "nonAssociation", "nonContact", "other"], "alcoholMonitoringDetail": "", "civilOrdersDetail": "Civil orders detail", "curfewDetail": "", "drugTestingDetail": "", "engagementWithServicesDetail": "", "exclusionZoneDetail": "", "initmateRelationshipsDetail": "", "noContactWithChildrenDetail": "", "nonAssociationDetail": "Non association detail", "nonContactDetail": "Non contact detail", "programmesDetail": "", "residencyRestrictionDetail": "", "otherDetail": "Other detail"}}, "prison-information": {"adjudications": {"adjudications": [{"id": 69927, "reportedAt": "2022-10-09", "establishment": "Hawthorne", "offenceDescription": "Nam vel nisi fugiat veniam possimus omnis.", "hearingHeld": false, "finding": "NOT_PROVED"}, {"id": 39963, "reportedAt": "2022-07-10", "establishment": "Oklahoma City", "offenceDescription": "Illum maxime enim explicabo soluta sequi voluptas.", "hearingHeld": true, "finding": "PROVED"}, {"id": 77431, "reportedAt": "2022-05-30", "establishment": "Jurupa Valley", "offenceDescription": "Quis porro nemo voluptates doloribus atque quis provident iure.", "hearingHeld": false, "finding": "PROVED"}]}, "acct-alerts": {"acctAlerts": [{"alertTypeDescription": "Theft", "dateCreated": "2022-12-05", "dateExpires": "2023-05-29", "description": "Soluta harum harum hic maxime reprehenderit quis harum necessitatibus."}, {"alertTypeDescription": "<PERSON><PERSON>", "dateCreated": "2022-05-21", "dateExpires": "2023-12-16", "description": "Quia ex nisi deserunt voluptatibus sit ipsa."}]}}, "approvals-for-specific-risks": {"approvals-for-specific-risks": {"approvals": "yes", "approvalsDetail": "Approvals detail"}}, "placement-considerations": {"accommodation-sharing": {"accommodationSharing": "yes", "accommodationSharingYesDetail": "Some detail", "accommodationSharingNoDetail": ""}, "cooperation": {"support": "Support detail"}, "anti-social-behaviour": {"concerns": "yes", "concernsDetail": "Anti-social behaviour concerns detail"}, "substance-misuse": {"substanceMisuse": "yes", "substanceMisuseDetail": "Details of drug and alcohol misuse"}, "rosh-level": {"riskToChildren": "Risk to children detail", "riskToPublic": "Risk to public detail", "riskToKnownAdult": "Risk to known adult detail", "riskToStaff": "Risk to staff detail", "riskToSelf": "Risk to self detail"}, "risk-management-plan": {"version": "2", "oasysImported": "2023-08-02", "oasysCompleted": "2023-08-02", "riskManagementAnswers": {"QRM30": "Some answer for the first risk management question. With an extra comment RM30", "QRM31": "Some answer for the second risk management question. With an extra comment RM31", "QRM32": "Some answer for the third risk management question. With an extra comment RM32", "QRM33": "Some answer for the fourth risk management question. With an extra comment RM33"}, "riskManagementSummaries": [{"questionNumber": "RM30", "label": "Supervision", "answer": "Some answer for the first risk management question. With an extra comment RM30"}, {"questionNumber": "RM31", "label": "Monitoring and control", "answer": "Some answer for the second risk management question. With an extra comment RM31"}, {"questionNumber": "RM32", "label": "Interventions and treatment", "answer": "Some answer for the third risk management question. With an extra comment RM32"}, {"questionNumber": "RM33", "label": "Victim safety planning", "answer": "Some answer for the fourth risk management question. With an extra comment RM33"}]}}, "behaviour-in-cas": {"previous-stays": {"previousStays": "yes"}, "previous-stays-details": {"accommodationTypes": ["cas1", "cas3"], "cas1Detail": "Approved Premises detail", "cas2Detail": "", "cas3Detail": "Transitional Accommodation (CAS3) detail"}}, "placement-location": {"alternative-pdu": {"alternativePdu": "yes", "pduId": "616497bf-3e6b-40e2-830b-8bfaeeaec157", "pduName": "County Durham and Darlington"}, "alternative-pdu-reason": {"reason": "Reasons X, Y, Z"}}, "disability-cultural-and-specific-needs": {"needs": {"needs": ["hearingImpairment", "neurodivergence", "physicalHealth", "other"], "hearingImpairmentDetail": "Hearing impairment detail", "languageDetail": "", "learningDisabilityDetail": "", "mentalHealthDetail": "", "mobilityDetail": "", "neurodivergenceDetail": "Neurodivergence detail", "physicalHealthDetail": "Physical health detail", "visualImpairmentDetail": "", "otherDetail": "Other needs detail"}, "property-attributes-or-adaptations": {"propertyAttributesOrAdaptations": "yes", "propertyAttributesOrAdaptationsDetail": "Adaptation requirements detail"}, "religious-or-cultural-needs": {"religiousOrCulturalNeeds": "yes", "religiousOrCulturalNeedsDetail": "Religious needs detail"}}, "safeguarding-and-support": {"safeguarding-and-vulnerability": {"concerns": "yes", "concernsDetail": "Concerns A, B, and C"}, "support-in-the-community": {"support": "Community support details"}, "local-connections": {"localConnections": "Local connection details"}, "caring-responsibilities": {"caringResponsibilities": "yes", "caringResponsibilitiesDetail": "Caring responsibilities X, Y, and Z"}}, "food-allergies": {"food-allergies": {"foodAllergies": "yes", "foodAllergiesDetail": "Allergic to X, Y, Z"}}, "move-on-plan": {"move-on-plan": {"plan": "A move on plan"}}, "accommodation-referral-details": {"dtr-submitted": {"dtrSubmitted": "yes"}, "dtr-details": {"reference": "ABC123", "date": "2022-04-12", "date-year": "2022", "date-month": "4", "date-day": "12", "localAuthorityAreaName": "Barking and Dagenham", "dutyToReferOutcome": "acceptedPreventionAndReliefDuty", "dutyToReferOutcomeOtherDetails": ""}, "crs-submitted": {"crsSubmitted": "yes"}, "other-accommodation-options": {"otherOptions": "yes", "otherOptionsDetail": "Other accommodation details"}}, "check-your-answers": {"review": {"reviewed": "1"}}}