{"compilerOptions": {"target": "es5", "noEmit": true, "lib": ["es5", "dom", "es2015.promise"], "types": ["cypress", "cypress-axe"], "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": ".", "skipLibCheck": true, "experimentalDecorators": true, "paths": {"@approved-premises/ui": ["../server/@types/ui/index.d.ts"], "@approved-premises/api": ["../server/@types/shared/index.d.ts"]}}, "include": ["**/*.ts"]}