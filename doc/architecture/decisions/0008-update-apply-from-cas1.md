# 8. Update Apply from CAS1

Date: 2023-02-23

## Status

Accepted

## Context

Part of the CAS3 service will be allowing CPPs to complete a referal form to be reviewed by HPTs, replacing the existing Word document that is currently used for this purpose. We refer to this feature as Apply. We branched our UI repository from the CAS1 UI repository in late 2022, when CAS1 had made limited progress on a similar feature. Since then, CAS1 have made significant progress on this feature. This includes:

* Importing details of a person on probation from nDelius using their CRN
* Selecting an index offence
* Presenting a linear series of question pages that together make up the application
* Displaying a page showing progress completing the application form
* Displaying a Check Your Anwsers page
* Allowing an application to be saved and resumed before submission
* Submitting an application for review

CAS1's and CAS3's Apply feature is not to be confused with Refer And Monitor, which is built to the specific needs of Commissioned Rehabilitative Services. There is potential in the future for these features to be considated, but this is out of scope given our current development timeline. 

## Decision

We will take the current state of CAS1 Apply as the starting point for CAS3 Apply. We will copy this feature from CAS1, removing CAS1 specific content, but keeping the architecture CAS1 have created to enable a multipage application form.

## Consequences

This allows us to reuse CAS1's work, rather than developing our own Apply feature from scratch. This also makes it easy to work with the existing Apply API, with the trandeoff that since we're sharing a set of API endpoints for this feature, we lose some flexibility to tailor the API to CAS3's specific needs. We expect CAS3's Apply to diverge from CAS1's Apply, even outside of service-specific content, and the exact extent is still to be determined, though this divergence will be constrained by the need to work with this shared API.

We inherit a number of architecture decisions from CAS1.

### Sections, tasks, and pages

The application form will be split into sections, which are split into tasks, which are further split into pages. Our `Apply` class lists its sections, which then lists its tasks, which then lists pages. Pages will be implemented as a code file under `server/form-pages/apply`, which an associated Nunjucks template under `server/views/applications`. Each page class inherits from our `TasklistPage` class, and defines their next and previous pages.

Pages will be largely without outside dependencies, though some pages may need to retrieve data from our API. Generally pages will be constructed directly using its constructor, though a page class may define a static, asynchronous, `initialize` method which is passed a `DataServices` object containing references to our service classes, which can be used to retrive any data necessary to render the form page.

### Persisting submitted documents in translated JSON

See [CAS1 ADR #5](https://github.com/ministryofjustice/hmpps-approved-premises-ui/blob/main/doc/architecture/decisions/0005-persist-submitted-documents-in-translated-json.md). As the user completes each page of the form, we persist machine-readable JSON data tracking their answers. However, we expect there to be changes to the application form after the feature is released, leading to a mismatch between our form pages, and the persisted JSON data. To allow existing applications to be viewed (though not edited) after a change to the form has been made, we also persist a "translated" document. This holds the user's answers in a human readable format that we can render back to them, even after the underlying form has changed.

Each page will be responsible for generating its part of this translated document.

### Use of decorators

See [CAS1 ADR #6](https://github.com/ministryofjustice/hmpps-approved-premises-ui/blob/main/doc/architecture/decisions/0006-use-decorators-to-define-form-pages-and-record-metadata.md). Each Apply page, task, and section will have metadata defined using decorators. This will describe which pages belong to each task, and which tasks belong to each section. Page decorators will also describe the format of the data generated from that page. This metadata will be used to define a JSON schema for the application form as a whole, which can be generated by running `npm run generate:schema:apply`. This schema can then be shared with the API to validate data submitted from the Apply feature.

### Versioning limitations

Though we persit a translated document that can be used as the form changes, as in CAS1, there is currently no infrastructure to migrate the persisted machine-readable data as the application form changes. Currently this will lead to validation failures for forms that have been started but not submitted at the point of any change to the form.
