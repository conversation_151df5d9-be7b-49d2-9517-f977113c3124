name: Security npm dependency check
on:
  workflow_dispatch:
  schedule:
    - cron: "22 6 * * MON-FRI" # Every weekday at 06:22 UTC
jobs:
  security-npm-dependency-check:
    name: Project security npm dependency check
    uses: ministryofjustice/hmpps-github-actions/.github/workflows/security_npm_dependency.yml@v2 # WORKFLOW_VERSION
    with:
      channel_id: ${{ vars.SECURITY_ALERTS_SLACK_CHANNEL_ID || 'NO_SLACK' }}
      node_version_file: ".node-version"
    secrets: inherit
