name: Generate Types

on:
  workflow_dispatch:

env:
  NODE_ENV: test
  API_CLIENT_ID: approved-premises
  API_CLIENT_SECRET: clientsecret

jobs:
  generate-types:
    runs-on: ubuntu-latest
    steps:
      - name: Generate Token
        uses: tibdex/github-app-token@v2
        id: generate-token
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.APP_PRIVATE_KEY }}
      - name: Check out code
        uses: actions/checkout@v4.2.2
      - name: Generate Types
        run: ./script/generate-types
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ steps.generate-token.outputs.token }}
          title: 'API model updates'
          commit-message: 'Updating hmpps-approved-premises-api models from OpenAPI specification'
          body: 'Updating hmpps-approved-premises-api models from OpenAPI specification.  This PR was created automatically from the generate-types.yml Workflow'
          delete-branch: true
          branch: update-api-types
