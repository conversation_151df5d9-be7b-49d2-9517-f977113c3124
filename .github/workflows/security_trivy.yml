name: Security trivy dependency check
on:
  workflow_dispatch:
  schedule:
    - cron: "22 6 * * MON-FRI" # Every weekday at 06:22 UTC
jobs:
  security-kotlin-trivy-check:
    name: Project security trivy dependency check
    uses: ministryofjustice/hmpps-github-actions/.github/workflows/security_trivy.yml@v2 # WORKFLOW_VERSION
    with:
      channel_id: ${{ vars.SECURITY_ALERTS_SLACK_CHANNEL_ID || 'NO_SLACK' }}
    secrets: inherit
