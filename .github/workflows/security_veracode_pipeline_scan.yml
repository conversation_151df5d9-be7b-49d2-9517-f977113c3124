name: Security veracode pipeline scan
on:
  workflow_dispatch:
  schedule:
    - cron: "22 6 * * MON-FRI" # Every weekday at 06:22 UTC
jobs:
  security-veracode-pipeline-scan:
    name: Project security veracode pipeline scan
    uses: ministryofjustice/hmpps-github-actions/.github/workflows/security_veracode_pipeline_scan.yml@v2 # WORKFLOW_VERSION
    with:
      channel_id: ${{ vars.SECURITY_ALERTS_SLACK_CHANNEL_ID || 'NO_SLACK' }}
    secrets: inherit
