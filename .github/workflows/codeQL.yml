name: CodeQL

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

jobs:
  codeQL:
    name: CodeQL
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.1.7

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: 'javascript'

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: '/language:javascript'
