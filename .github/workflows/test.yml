name: Test

on:
  pull_request:
    branches:
      - main
  workflow_call:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  helm_lint:
    name: "<PERSON><PERSON> config linting 🔎"
    uses: ministryofjustice/hmpps-github-actions/.github/workflows/test_helm_lint.yml@v2
    strategy:
      matrix:
        environments: [ 'development', 'test', 'preprod', 'prod' ]
    with:
      environment: ${{ matrix.environments }}

  node_build:
    name: "Build 🛠️"
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --no-audit --no-audit

      - name: Run build
        run: npm run build

  type_checking:
    name: "Type check 🔎"
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --no-audit

      - name: Pulling the latest type from the API repo
        run: npm run generate-types

      - name: Run type check
        run: npm run typecheck

  linting:
    name: "Linting 🔎"
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --no-audit

      - name: Run lint checks
        run: npm run lint

      - name: Run shell scripts lint checks
        run: npm run shellcheck

  unit_test:
    name: "Unit testing 🧪"
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --no-audit

      - name: Run Unit tests
        run: npm run test:ci

      - name: Check coverage
        run: |
          npx nyc report -t ./coverage --reporter=text --reporter=text-summary
          npx nyc check-coverage -t ./coverage

  integration_test:
    name: "Integration testing 🧪"
    runs-on: ubuntu-22.04
    steps:
      - name: Check out code
        uses: actions/checkout@v4.2.2

      - name: Setup Node.js environment
        uses: actions/setup-node@v4.4.0
        with:
          node-version-file: '.node-version'
          cache: 'npm'

      - name: Installing dependencies
        run: npm ci

      - name: Building source
        run: npm run build

      - name: Running Integration tests
        run: npm run test:integration

      - name: Upload Integration test artefacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-artefacts
          path: |
            integration_tests/videos
            integration_tests/screenshots
          retention-days: 30
          if-no-files-found: ignore
