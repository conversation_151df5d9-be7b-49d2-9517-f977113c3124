---
generic-service:
  nameOverride: hmpps-temporary-accommodation-ui
  serviceAccountName: hmpps-temporary-accommodation-service-account

  replicaCount: 4

  image:
    repository: ghcr.io/ministryofjustice/hmpps-temporary-accommodation-ui
    tag: app_version # override at deployment time
    port: 3000

  ingress:
    enabled: true
    hosts:
      - app-hostname.local # override per environment
    tlsSecretName: temporary-accommodation-ui-cert # override per environment
    v1_2_enabled: true
    v0_47_enabled: false

  livenessProbe:
    httpGet:
      path: /ping

  readinessProbe:
    httpGet:
      path: /ping

  custommetrics:
    enabled: true
    scrapeInterval: 15s
    metricsPath: /metrics
    metricsPort: 3001

  # Environment variables to load into the deployment
  env:
    NODE_ENV: 'production'
    REDIS_TLS_ENABLED: 'true'
    TOKEN_VERIFICATION_ENABLED: 'true'
    APPLICATIONINSIGHTS_CONNECTION_STRING: 'InstrumentationKey=$(APPINSIGHTS_INSTRUMENTATIONKEY);IngestionEndpoint=https://northeurope-0.in.applicationinsights.azure.com/;LiveEndpoint=https://northeurope.livediagnostics.monitor.azure.com/'
    COMMUNITY_ACCOMMODATION_API_SERVICE_NAME: 'temporary-accommodation'
    AUDIT_SERVICE_NAME: 'hmpps-temporary-accommodation-ui'
    AUDIT_SQS_REGION: 'eu-west-2'

  # Pre-existing kubernetes secrets to load as environment variables in the deployment.
  # namespace_secrets:
  #   [name of kubernetes secret]:
  #     [name of environment variable as seen by app]: [key of kubernetes secret to load]

  namespace_secrets:
    hmpps-temporary-accommodation-ui:
      APPINSIGHTS_INSTRUMENTATIONKEY: 'APPINSIGHTS_INSTRUMENTATIONKEY'
      API_CLIENT_ID: 'API_CLIENT_ID'
      API_CLIENT_SECRET: 'API_CLIENT_SECRET'
      SYSTEM_CLIENT_ID: 'SYSTEM_CLIENT_ID'
      SYSTEM_CLIENT_SECRET: 'SYSTEM_CLIENT_SECRET'
      SESSION_SECRET: 'SESSION_SECRET'
      SENTRY_DSN: 'SENTRY_DSN'
    elasticache-redis:
      REDIS_HOST: 'primary_endpoint_address'
      REDIS_AUTH_TOKEN: 'auth_token'

  allowlist:
    groups:
      - internal
      - prisons
    palo-alto-prisma-access-corporate: "************/26"

generic-prometheus-alerts:
  targetApplication: hmpps-temporary-accommodation-ui
