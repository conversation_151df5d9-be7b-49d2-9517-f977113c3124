---
# Per environment values which override defaults in hmpps-temporary-accommodation-ui/values.yaml

generic-service:
  replicaCount: 2

  ingress:
    hosts:
      - temporary-accommodation-dev.hmpps.service.justice.gov.uk
      - transitional-accommodation-dev.hmpps.service.justice.gov.uk
    contextColour: green
    tlsSecretName: hmpps-temporary-accommodation-dev-cert

  env:
    ENVIRONMENT: dev
    APPROVED_PREMISES_API_URL: 'https://approved-premises-api-dev.hmpps.service.justice.gov.uk'
    FIRST_INGRESS_URL: 'https://temporary-accommodation-dev.hmpps.service.justice.gov.uk'
    SECOND_INGRESS_URL: 'https://transitional-accommodation-dev.hmpps.service.justice.gov.uk'
    HMPPS_AUTH_URL: 'https://sign-in-dev.hmpps.service.justice.gov.uk/auth'
    TOKEN_VERIFICATION_API_URL: 'https://token-verification-api-dev.prison.service.justice.gov.uk'
    AUDIT_LOG_ERRORS: 'true'
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_RESPONSE: 30000
    COMMUNITY_ACCOMMODATION_API_TIMEOUT_DEADLINE: 30000
    PAGINATION_ASSESSMENTS_DEFAULT_PAGE_SIZE: 100
    DOMAIN_EVENTS_EMIT_ENABLED: 'personArrived,personDeparted'
    MANAGE_PROPERTIES_V2_ENABLED: true

  namespace_secrets:
    sqs-hmpps-audit-secret:
      AUDIT_SQS_QUEUE_URL: "sqs_queue_url"
      AUDIT_SQS_QUEUE_NAME: "sqs_queue_name"

  allowlist: null

generic-prometheus-alerts:
  alertSeverity: hmpps-approved-premises
