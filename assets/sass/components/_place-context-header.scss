.place-context-header {
  @include govuk-responsive-padding(4, 'top');
  @include govuk-responsive-padding(4, 'bottom');
  background-color: govuk-colour('blue');

  .govuk-phase-banner + & {
    margin-top: -1px;
  }
}

.place-context-header__heading {
  color: govuk-colour('white');
}

.place-context-header__attributes {
  @include govuk-media-query($from: tablet) {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0;
  }
}

.place-context-header__row {
  @include govuk-media-query($from: tablet) {
    @include govuk-responsive-padding(4, 'right');

    display: flex;
    flex-wrap: wrap;
  }

  &:last-child {
    padding-right: 0;
  }
}

.place-context-header__key,
.place-context-header__value {
  display: inline-block;
  color: govuk-colour('white');
  padding: 0;
  margin-left: 0;
}

.place-context-header__key {
  @include govuk-responsive-padding(1, 'right');
}
