.booking-info {
  @include govuk-responsive-margin(8, "bottom");

  border: $govuk-border-width solid govuk-colour('light-grey');

  background-color: govuk-colour('light-grey');

  &:focus {
    outline: $govuk-focus-width solid $govuk-focus-colour;
  }
}

.booking-info__header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;

  padding: 2px govuk-spacing(3) govuk-spacing(1);

  // Ensures the notification header appears separate to the notification body text in high contrast mode
  border-bottom: 1px solid transparent;

  @include govuk-media-query($from: tablet) {
    padding: 2px govuk-spacing(4) govuk-spacing(1);
  }
}

.booking-info__title {
  margin: 0;
  padding: 0;
}

.booking-info__history-link {
  margin: 0;
  padding: 0;
}

.booking-info__content {
  $padding-tablet: govuk-spacing(4);
  @include govuk-text-colour;
  padding: govuk-spacing(3);

  background-color: $govuk-body-background-colour;

  @include govuk-media-query($from: tablet) {
    padding: $padding-tablet;
  }

  // Wrap content at the same place that a 2/3 grid column ends, to maintain
  // shorter line-lengths when the notification banner is full width
  > * {
    // When elements have their own padding (like lists), include the padding
    // in the max-width calculation
    box-sizing: border-box;

    // Calculate the internal width of a two-thirds column...
    $two-col-width: calc(($govuk-page-width * 2 / 3) - ($govuk-gutter * 1 / 3));

    // ...and then factor in the left border and padding
    $banner-exterior: ($padding-tablet + $govuk-border-width);
    max-width: $two-col-width - $banner-exterior;
  }

  > :last-child {
    margin-bottom: 0;
  }

  .govuk-summary-list {
    width: 100%;
    max-width: none;
  }

  .govuk-summary-list__row {
    border-width: 2px;
    border-color: govuk-colour('light-grey');
    width: 100%;
  }

  .govuk-summary-list__row:last-child {
    border-bottom: 0;
  }
}
